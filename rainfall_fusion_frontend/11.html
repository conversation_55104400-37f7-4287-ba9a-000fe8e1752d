<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多源降雨产品融合系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            padding: 0;
            overflow: hidden;
        }        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: transparent;
            border-radius: 0;
            box-shadow: none;
            display: flex;
            flex-direction: column;
        }        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 15px 30px;
            flex-shrink: 0;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            height: 80px;
            display: flex;
            align-items: center;
        }

        .nav-container {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-left h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .nav-left p {
            font-size: 0.9em;
            opacity: 0.9;
            margin: 0;
        }

        .main-nav {
            display: flex;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9em;
            border: 2px solid transparent;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }.main-content {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }        .left-panel {
            width: 40%;
            flex-shrink: 0;
            position: fixed;
            left: 0;
            top: 80px;
            bottom: 0;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            overflow-y: auto;
            z-index: 100;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .left-panel::-webkit-scrollbar {
            width: 6px;
        }

        .left-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .left-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }        .right-panel {
            margin-left: 40%;
            width: 60%;
            height: calc(100vh - 80px);
            margin-top: 80px;
            overflow-y: auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .right-panel::-webkit-scrollbar {
            width: 8px;
        }

        .right-panel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .right-panel::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }        .input-section {
            background: transparent;
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            height: 100%;
        }.input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
        }

        .input-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
            font-size: 0.9em;
        }

        .input-group input, .input-group select {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 13px;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .data-input-section {
            margin-top: 20px;
        }        .data-input-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .data-input-group {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
        }

        .data-input-group h4 {
            margin-bottom: 10px;
            color: #2c3e50;
            font-size: 0.9em;
        }        .data-input-group textarea {
            width: 100%;
            height: 120px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            resize: vertical;
        }

        /* 数据表格样式 */
        .data-table-container {
            width: 100%;
            height: 200px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 11px;
            font-family: monospace;
            table-layout: fixed;
        }

        .data-table thead {
            background: #f8f9fa;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th {
            padding: 8px 6px;
            border: 1px solid #ddd;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
            background: #f8f9fa;
            box-sizing: border-box;
        }

        .data-table td {
            padding: 0;
            border: 1px solid #ddd;
            text-align: center;
            box-sizing: border-box;
        }

        .data-table td input {
            width: 100%;
            height: 100%;
            border: none;
            background: transparent;
            text-align: center;
            font-size: 10px;
            font-family: monospace;
            padding: 4px 6px;
            box-sizing: border-box;
        }

        .data-table td input:focus {
            outline: 2px solid #667eea;
            background: #f0f8ff;
        }

        .data-table tbody {
            display: block;
            height: calc(200px - 34px);
            overflow-y: auto;
            overflow-x: hidden;
        }

        .data-table thead,
        .data-table tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .data-table .time-col {
            width: calc(65% - 8px);
        }

        .data-table .value-col {
            width: calc(35% - 8px);
        }

        /* 滚动条样式 */
        .data-table tbody::-webkit-scrollbar {
            width: 8px;
        }

        .data-table tbody::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .data-table tbody::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .data-table tbody::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            gap: 8px;
        }

        .table-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .table-btn:hover {
            background: #5a6fd8;
        }

        .table-btn.danger {
            background: #dc3545;
        }

        .table-btn.danger:hover {
            background: #c82333;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 2px solid #e1e8ed;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        /* 文件上传样式 */
        .file-upload-section {
            margin-top: 20px;
        }

        .file-upload-container {
            margin-bottom: 10px;
        }

        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 10px;
            padding: 12px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }

        .file-upload-area:hover {
            border-color: #5a6fd8;
            background: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .file-upload-area.dragover {
            border-color: #4caf50;
            background: #f1f8e9;
        }

        .file-upload-icon {
            font-size: 1.5em;
            margin-bottom: 6px;
            color: #667eea;
        }

        .file-upload-text {
            color: #2c3e50;
            font-size: 13px;
        }

        .file-upload-text strong {
            color: #667eea;
        }

        .file-upload-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-size: 13px;
        }

        .file-upload-status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .file-upload-status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }

        .file-format-hint {
            margin-top: 10px;
            padding: 8px 12px;
            background: #f0f8ff;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }

        /* 统计分析图表样式 */
        .stats-charts-container {
            margin-top: 20px;
        }

        .stats-chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-chart-item {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .stats-chart-item h5 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .stats-chart-wrapper {
            height: 250px;
            position: relative;
        }        .button-group {
            display: flex;
            flex-direction: row;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            flex: 1;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #8b4513;
            box-shadow: 0 5px 15px rgba(252, 182, 159, 0.4);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(252, 182, 159, 0.6);
        }        .results-section {
            margin-top: 0;
        }

        .result-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid #e1e8ed;
        }

        .result-card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            font-size: 1.3em;
            font-weight: 600;
        }

        .result-card-content {
            padding: 25px;
        }

        .stats-table, .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            font-size: 14px;
        }

        .stats-table th, .stats-table td,
        .metrics-table th, .metrics-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #ddd;
        }

        .stats-table th, .metrics-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .stats-table tbody tr:nth-child(even),
        .metrics-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }

        .stats-table tbody tr:hover,
        .metrics-table tbody tr:hover {
            background: #e3f2fd;
            transition: background-color 0.3s ease;
        }        .chart-container {
            margin-top: 20px;
            height: 500px;
            position: relative;
        }

        .fusion-result {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #4caf50;
        }

        .fusion-result h4 {
            color: #2e7d32;
            margin-bottom: 15px;
        }

        .fusion-data {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
            gap: 8px;
            font-family: monospace;
            font-size: 12px;
        }

        .fusion-data span {
            background: white;
            padding: 5px;
            border-radius: 4px;
            text-align: center;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #f44336;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tab-container {
            display: flex;
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: #f8f9fa;
            border: 1px solid #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .tab:first-child {
            border-radius: 10px 0 0 10px;
        }

        .tab:last-child {
            border-radius: 0 10px 10px 0;
        }

        .tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            display: none;
        }        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            gap: 10px;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .pagination-btn:hover {
            background: #f0f0f0;
            border-color: #999;
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .pagination-btn:disabled {
            background: #f5f5f5;
            color: #ccc;
            cursor: not-allowed;
            border-color: #e0e0e0;
        }

        .pagination-info {
            font-size: 14px;
            color: #666;
            margin: 0 15px;
        }        /* 表格容器样式 */
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 展开/折叠样式 */
        .collapsible-section {
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .collapsible-header {
            background: #f8f9fa;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s ease;
        }

        .collapsible-header:hover {
            background: #e9ecef;
        }

        .collapsible-header h4 {
            margin: 0;
            color: #2c3e50;
        }

        .collapsible-toggle {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            transition: transform 0.3s ease;
        }

        .collapsible-content {
            padding: 20px;
            display: none;
        }

        .collapsible-content.active {
            display: block;
        }

        .collapsible-section.expanded .collapsible-toggle {
            transform: rotate(180deg);
        }

        /* 数据预览样式 */
        .data-preview {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #667eea;
        }

        .data-preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .data-preview-stats {
            font-size: 12px;
            color: #666;
        }

        .view-full-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.3s ease;
        }

        .view-full-btn:hover {
            background: #5a6fd8;
        }

        /* 面包屑导航 */
        .breadcrumb {
            background: #f8f9fa;
            padding: 10px 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-size: 14px;
        }

        .breadcrumb a {
            color: #667eea;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }        .breadcrumb .separator {
            margin: 0 8px;
            color: #666;
        }        /* 回到顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 40px;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
        }

        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }        /* 页面指示器 */
        .page-indicator {
            position: fixed;
            right: 30px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        @media (max-width: 768px) {
            .page-indicator {
                bottom: 100px;
                top: auto;
                right: 20px;
                transform: none;
                flex-direction: row;
                gap: 8px;
            }
        }

        .indicator-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .indicator-dot.active {
            background: #667eea;
            transform: scale(1.2);
            border-color: white;
        }        .indicator-dot:hover {
            background: #667eea;
            transform: scale(1.1);
        }

        /* 加载指示器 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .loading-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .loading-spinner {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            font-size: 16px;
            color: #333;
            font-weight: 600;
        }        @media (max-width: 768px) {
            body {
                overflow: auto;
            }
            
            .header {
                position: relative;
                height: auto;
                padding: 15px;
            }
            
            .main-content {
                flex-direction: column;
                height: auto;
                overflow: visible;
            }
            
            .left-panel {
                width: 100%;
                position: static;
                height: auto;
                background: rgba(248, 249, 250, 0.95);
                margin: 0;
                padding: 20px;
                box-shadow: none;
            }
            
            .right-panel {
                width: 100%;
                margin-left: 0;
                margin-top: 0;
                height: auto;
                padding: 20px;
            }
            
            .input-grid {
                grid-template-columns: 1fr;
            }
            
            .data-input-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .tab-container {
                flex-direction: column;
            }
            
            .tab {
                border-radius: 0 !important;
            }
            
            .tab:first-child {
                border-radius: 10px 10px 0 0 !important;
            }
            
            .tab:last-child {
                border-radius: 0 0 10px 10px !important;
            }

            .stats-chart-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }        @media (max-width: 1200px) and (min-width: 769px) {
            .left-panel {
                width: 380px;
            }
            
            .data-input-group textarea {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav-container">
                <div class="nav-left">
                    <h1>🌧️ 多源降雨产品融合系统</h1>
                    <p>Multi-Source Rainfall Product Fusion System</p>
                </div>
                <div class="nav-right">
                    <nav class="main-nav">
                        <a href="11.html" class="nav-link active">🌧️ 降雨产品融合</a>
                        <a href="flow_prediction.html" class="nav-link">📊 流量预测系统</a>
                    </nav>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- 左侧面板 - 数据输入配置 -->
            <div class="left-panel">
                <div class="input-section" id="input-section">
                    <h3 style="margin-bottom: 20px; color: #2c3e50; text-align: center;">📊 数据输入配置</h3>
                    
                    <div class="input-grid">
                        <div class="input-group">
                            <label for="startTime">起始时间:</label>
                            <input type="datetime-local" id="startTime" value="2024-01-01T00:00">
                        </div>
                        <div class="input-group">
                            <label for="endTime">结束时间:</label>
                            <input type="datetime-local" id="endTime" value="2024-01-01T23:00">
                        </div>
                        <div class="input-group">
                            <label for="frequency">预报频率:</label>
                            <select id="frequency">
                                <option value="1">1小时</option>
                                <option value="3">3小时</option>
                                <option value="6">6小时</option>
                                <option value="12">12小时</option>
                                <option value="24">24小时</option>
                            </select>
                        </div>
                    </div>

                    <div class="data-input-section">
                        <h4 style="margin-bottom: 15px; color: #2c3e50; font-size: 0.9em;">降雨数据输入 (表格格式: 时间戳 + 降水量，单位: mm)</h4>
                        <div class="data-input-grid">
                            <div class="data-input-group">
                                <h4>产品1预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product1Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product1TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product1')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product1')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product1Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>产品2预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product2Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product2TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product2')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product2')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product2Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>产品3预报数据</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="product3Table">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="product3TableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('product3')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('product3')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="product3Count">0 行</span>
                                </div>
                            </div>
                            <div class="data-input-group">
                                <h4>实测雨量 (可选)</h4>
                                <div class="data-table-container">
                                    <table class="data-table" id="observedTable">
                                        <thead>
                                            <tr>
                                                <th class="time-col">时间</th>
                                                <th class="value-col">降水量(mm)</th>
                                            </tr>
                                        </thead>
                                        <tbody id="observedTableBody">
                                            <!-- 动态生成行 -->
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-controls">
                                    <div>
                                        <button class="table-btn" onclick="addTableRow('observed')">+ 添加行</button>
                                        <button class="table-btn danger" onclick="clearTable('observed')">清空</button>
                                    </div>
                                    <span style="font-size: 10px; color: #666;" id="observedCount">0 行</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Excel文件上传区域 -->
                    <div class="file-upload-section">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h4 style="margin: 0; color: #2c3e50; font-size: 0.9em;">📁 Excel文件上传</h4>
                            <div style="width: 16.67%; text-align: right;">
                                <button class="btn" style="background: #6c757d; color: white; border: none; font-size: 10px; padding: 4.5px 6px; border-radius: 3px; white-space: nowrap; width: 100%;" onclick="clearAllData()">
                                    🗑️ 清空数据
                                </button>
                            </div>
                        </div>
                        <div class="file-upload-container">
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;" onchange="handleFileUpload(event)">
                            <div class="file-upload-area" onclick="document.getElementById('excelFileInput').click()">
                                <div class="file-upload-icon">📄</div>
                                <div class="file-upload-text">
                                    <strong>点击上传Excel文件</strong>
                                    <br>
                                    <small>支持 .xlsx 和 .xls 格式</small>
                                </div>
                            </div>
                            <div id="fileUploadStatus" class="file-upload-status" style="display: none;"></div>
                        </div>
                        <div class="file-format-hint">
                            <small style="color: #666;">
                                <strong>📋 文件格式要求:</strong> Excel文件应包含时间戳列和4个数值列，分别对应时间、产品1、产品2、产品3和实测雨量(可选)
                            </small>
                        </div>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-primary" onclick="performStatisticalAnalysis()">📈 统计分析</button>
                        <button class="btn btn-secondary" onclick="performDataFusion()">🔄 数据融合</button>
                    </div>

                    <div style="margin-top: 15px; text-align: center;">
                        <button class="btn" style="background: #17a2b8; color: white; font-size: 12px; padding: 8px 16px;" onclick="generateTimeSeriesTables()">
                            🕒 自动生成时间序列表格
                        </button>
                    </div>

                    <div style="margin-top: 15px; padding: 10px; background: #f0f8ff; border-radius: 8px; font-size: 11px; color: #666;">
                        <strong>💡 快捷键提示:</strong> 
                        Ctrl+1/2/3 切换标签页 | ←→ 分页导航 | ESC 返回主页
                    </div>
                </div>
            </div>

            <!-- 右侧面板 - 结果显示 -->
            <div class="right-panel">
                <!-- 当没有结果时显示的欢迎信息 -->
                <div id="welcomeMessage" class="result-card">
                    <div class="result-card-header">🌧️ 欢迎使用多源降雨产品融合系统</div>
                    <div class="result-card-content">
                        <div style="text-align: center; padding: 40px 20px;">
                            <div style="font-size: 4em; margin-bottom: 20px;">📊</div>
                            <h3 style="color: #2c3e50; margin-bottom: 15px;">开始您的数据分析</h3>
                            <p style="color: #666; margin-bottom: 25px; line-height: 1.6;">
                                请在左侧面板中输入降雨预报数据，然后选择进行统计分析或数据融合操作。
                            </p>
                            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #2e7d32; margin-bottom: 8px;">📈 统计分析</h4>
                                    <p style="font-size: 14px; color: #666;">分析各产品的基础统计特征</p>
                                </div>
                                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px; flex: 1; min-width: 200px;">
                                    <h4 style="color: #1976d2; margin-bottom: 8px;">🔄 数据融合</h4>
                                    <p style="font-size: 14px; color: #666;">融合多源数据并评估效果</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="results-section" id="resultsSection" style="display: none;">
                <div class="breadcrumb">
                    <a href="#" onclick="showMainView()">🏠 主页</a>
                    <span class="separator">></span>
                    <span id="currentPage">分析结果</span>
                </div>

                <div class="tab-container">
                    <div class="tab active" onclick="switchTab('stats')">📊 统计分析</div>
                    <div class="tab" onclick="switchTab('fusion')">🔄 融合结果</div>
                    <div class="tab" onclick="switchTab('detailed')">📋 详细数据</div>
                </div>

                <div id="statsContent" class="tab-content active">
                    <div class="result-card">
                        <div class="result-card-header">📊 数据统计分析结果</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📈 基础统计信息</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="statsTableContainer"></div>
                                </div>
                            </div>

                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 数据可视化图表</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div class="stats-charts-container">
                                        <div class="stats-chart-grid">
                                            <div class="stats-chart-item">
                                                <h5>产品1预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product1Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>产品2预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product2Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>产品3预报数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="product3Chart"></canvas>
                                                </div>
                                            </div>
                                            <div class="stats-chart-item">
                                                <h5>实测雨量数据</h5>
                                                <div class="stats-chart-wrapper">
                                                    <canvas id="observedChart"></canvas>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="fusionContent" class="tab-content">
                    <div class="result-card">
                        <div class="result-card-header">🔄 多源数据融合结果</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📁 融合结果导出</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="fusionResults"></div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 可视化图表</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div class="chart-container">
                                        <canvas id="fusionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📋 精度评估指标</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    <div id="metricsTableContainer"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="detailedContent" class="tab-content">
                    <div class="result-card">
                        <div class="result-card-header">📋 详细数据视图</div>
                        <div class="result-card-content">
                            <div class="collapsible-section expanded">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>🔢 原始数据预览</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content active">
                                    <div id="rawDataPreview"></div>
                                </div>
                            </div>
                            
                            <div class="collapsible-section">
                                <div class="collapsible-header" onclick="toggleCollapsible(this)">
                                    <h4>📊 分页数据表格</h4>
                                    <span class="collapsible-toggle">▼</span>
                                </div>
                                <div class="collapsible-content">
                                    <div id="paginatedTableContainer"></div>
                                </div>                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        ⬆️
    </button>

    <!-- 页面指示器 -->
    <div class="page-indicator">
        <div class="indicator-dot active" onclick="scrollToSection('header')" title="顶部"></div>
        <div class="indicator-dot" onclick="scrollToSection('input-section')" title="数据输入"></div>
        <div class="indicator-dot" onclick="scrollToSection('resultsSection')" title="分析结果"></div>
    </div>

    <!-- 加载指示器 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <div class="loading-text" id="loadingText">正在处理数据...</div>
        </div>
    </div><script>
        let currentChart = null;
        let currentPage = 1;
        let itemsPerPage = 10;
        let allDataPoints = [];

        function parseData(dataString) {
            if (!dataString.trim()) return [];

            const lines = dataString.trim().split('\n');
            const parsedData = [];

            lines.forEach((line, index) => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                // 检查是否包含逗号分隔符
                if (trimmedLine.includes(',')) {
                    const parts = trimmedLine.split(',');
                    if (parts.length >= 2) {
                        const timestamp = parts[0].trim();
                        const value = parseFloat(parts[1].trim());

                        if (!isNaN(value)) {
                            parsedData.push({
                                timestamp: timestamp,
                                value: value
                            });
                        }
                    }
                } else {
                    // 兼容旧格式（仅数值）
                    const value = parseFloat(trimmedLine);
                    if (!isNaN(value)) {
                        // 生成默认时间戳
                        const baseTime = new Date('2024-01-01 09:00:00');
                        const timestamp = new Date(baseTime.getTime() + index * 3600000).toISOString().slice(0, 19).replace('T', ' ');
                        parsedData.push({
                            timestamp: timestamp,
                            value: value
                        });
                    }
                }
            });

            return parsedData;
        }

        // 从表格中获取数据
        function getTableData(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            const rows = tbody.querySelectorAll('tr');
            const data = [];

            rows.forEach(row => {
                const timeInput = row.querySelector('.time-input');
                const valueInput = row.querySelector('.value-input');

                if (timeInput && valueInput && timeInput.value.trim() && valueInput.value.trim()) {
                    const timestamp = timeInput.value.trim();
                    const value = parseFloat(valueInput.value.trim());

                    if (!isNaN(value)) {
                        data.push({
                            timestamp: timestamp,
                            value: value
                        });
                    }
                }
            });

            return data;
        }

        // 添加表格行
        function addTableRow(productName, timestamp = '', value = '') {
            const tbody = document.getElementById(productName + 'TableBody');
            const row = document.createElement('tr');

            row.innerHTML = `
                <td><input type="text" class="time-input" value="${timestamp}" placeholder="YYYY-MM-DD HH:MM"></td>
                <td><input type="number" class="value-input" value="${value}" placeholder="0.0" step="0.001"></td>
            `;

            tbody.appendChild(row);
            updateRowCount(productName);
        }

        // 清空表格
        function clearTable(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            tbody.innerHTML = '';
            updateRowCount(productName);
        }

        // 更新行数显示
        function updateRowCount(productName) {
            const tbody = document.getElementById(productName + 'TableBody');
            const count = tbody.querySelectorAll('tr').length;
            const countElement = document.getElementById(productName + 'Count');
            if (countElement) {
                countElement.textContent = `${count} 行`;
            }
        }

        // 填充表格数据
        function fillTableData(productName, data) {
            clearTable(productName);

            data.forEach(item => {
                if (typeof item === 'string' && item.includes(',')) {
                    const parts = item.split(',');
                    const timestamp = parts[0].trim();
                    const value = parts[1].trim();
                    addTableRow(productName, timestamp, value);
                } else if (typeof item === 'object' && item.timestamp && item.value !== undefined) {
                    addTableRow(productName, item.timestamp, item.value);
                }
            });
        }

        // Excel文件处理函数
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            const statusDiv = document.getElementById('fileUploadStatus');
            statusDiv.style.display = 'block';
            statusDiv.className = 'file-upload-status';
            statusDiv.innerHTML = '📤 正在读取文件...';

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });

                    // 获取第一个工作表
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // 转换为JSON格式
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (jsonData.length === 0) {
                        throw new Error('Excel文件为空');
                    }

                    // 解析数据
                    parseExcelData(jsonData, file.name);

                } catch (error) {
                    showUploadError('文件读取失败: ' + error.message);
                }
            };

            reader.onerror = function() {
                showUploadError('文件读取失败，请检查文件格式');
            };

            reader.readAsArrayBuffer(file);
        }

        function parseExcelData(jsonData, fileName) {
            try {
                // 跳过可能的标题行，从第一行或第二行开始读取数据
                let startRow = 0;

                // 检查第一行是否为标题行
                if (jsonData.length > 1) {
                    const firstRow = jsonData[0];
                    const isHeaderRow = firstRow.some(cell =>
                        typeof cell === 'string' &&
                        (cell.includes('时间') || cell.includes('Time') || cell.includes('产品') || cell.includes('Product') || cell.includes('实测') || cell.includes('Observed'))
                    );
                    if (isHeaderRow) {
                        startRow = 1;
                    }
                }

                const dataRows = jsonData.slice(startRow);

                if (dataRows.length === 0) {
                    throw new Error('没有找到有效的数据行');
                }

                // 提取各列数据
                const product1Data = [];
                const product2Data = [];
                const product3Data = [];
                const observedData = [];

                dataRows.forEach((row, index) => {
                    if (row.length < 4) {
                        throw new Error(`第${startRow + index + 1}行数据不完整，至少需要4列数据（时间戳 + 3个数值列）`);
                    }

                    // 第一列为时间戳，后面为数值列
                    const timestamp = row[0];
                    const val1 = parseFloat(row[1]);
                    const val2 = parseFloat(row[2]);
                    const val3 = parseFloat(row[3]);
                    const val4 = row.length > 4 ? parseFloat(row[4]) : null;

                    if (isNaN(val1) || isNaN(val2) || isNaN(val3)) {
                        throw new Error(`第${startRow + index + 1}行包含无效数值`);
                    }

                    // 格式化时间戳
                    let formattedTimestamp = timestamp;
                    if (timestamp instanceof Date) {
                        formattedTimestamp = timestamp.toISOString().slice(0, 19).replace('T', ' ');
                    } else if (typeof timestamp === 'string') {
                        formattedTimestamp = timestamp;
                    } else if (typeof timestamp === 'number') {
                        // Excel日期序列号转换
                        const excelDate = new Date((timestamp - 25569) * 86400 * 1000);
                        formattedTimestamp = excelDate.toISOString().slice(0, 19).replace('T', ' ');
                    }

                    product1Data.push(`${formattedTimestamp},${val1}`);
                    product2Data.push(`${formattedTimestamp},${val2}`);
                    product3Data.push(`${formattedTimestamp},${val3}`);

                    if (val4 !== null && !isNaN(val4)) {
                        observedData.push(`${formattedTimestamp},${val4}`);
                    }
                });

                // 填充到表格并添加视觉反馈
                fillTableWithAnimation('product1', product1Data, '产品1');
                fillTableWithAnimation('product2', product2Data, '产品2');
                fillTableWithAnimation('product3', product3Data, '产品3');

                if (observedData.length === product1Data.length) {
                    fillTableWithAnimation('observed', observedData, '实测雨量');
                } else {
                    // 清空实测雨量表格
                    clearTable('observed');
                }

                // 显示成功信息和数据预览
                const statusDiv = document.getElementById('fileUploadStatus');
                statusDiv.className = 'file-upload-status success';

                // 生成数据预览
                const previewData = generateDataPreviewWithTimestamp(product1Data, product2Data, product3Data, observedData);

                statusDiv.innerHTML = `
                    ✅ 文件上传成功！<br>
                    📊 已读取 ${product1Data.length} 行数据<br>
                    📁 文件名: ${fileName}<br>
                    📋 数据分布：产品1(${product1Data.length}行) | 产品2(${product2Data.length}行) | 产品3(${product3Data.length}行)
                    ${observedData.length === product1Data.length ? ' | 实测雨量(' + observedData.length + '行)' : '<br>⚠️ 实测雨量数据不完整或缺失'}
                    <br><br>
                    <div style="margin-top: 10px;">
                        <strong>📋 数据预览 (前5行):</strong>
                        ${previewData}
                    </div>
                `;

            } catch (error) {
                showUploadError(error.message);
            }
        }

        function showUploadError(message) {
            const statusDiv = document.getElementById('fileUploadStatus');
            statusDiv.className = 'file-upload-status error';
            statusDiv.innerHTML = `❌ ${message}`;
        }

        // 带动画效果的表格填充函数
        function fillTableWithAnimation(productName, data, label) {
            const parentGroup = document.getElementById(productName + 'Table').closest('.data-input-group');

            // 添加高亮效果
            parentGroup.style.transition = 'all 0.3s ease';
            parentGroup.style.transform = 'scale(1.02)';
            parentGroup.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            parentGroup.style.borderColor = '#667eea';

            // 清空并逐步填充数据
            clearTable(productName);

            setTimeout(() => {
                fillTableData(productName, data);

                // 显示数据行数提示
                const dataCount = data.length;
                const header = parentGroup.querySelector('h4');
                const originalText = header.textContent;
                header.innerHTML = `${originalText} <span style="color: #4caf50; font-size: 0.8em;">(已填充 ${dataCount} 行)</span>`;

                // 恢复原始样式
                setTimeout(() => {
                    parentGroup.style.transform = 'scale(1)';
                    parentGroup.style.boxShadow = '';
                    parentGroup.style.borderColor = '#e1e8ed';

                    // 3秒后移除行数提示
                    setTimeout(() => {
                        header.textContent = originalText;
                    }, 3000);
                }, 500);
            }, 200);
        }

        // 带动画效果的文本框填充函数（兼容性保留）
        function fillTextAreaWithAnimation(elementId, data, label) {
            const textarea = document.getElementById(elementId);
            if (!textarea) return; // 如果找不到textarea，跳过

            const parentGroup = textarea.closest('.data-input-group');

            // 添加高亮效果
            parentGroup.style.transition = 'all 0.3s ease';
            parentGroup.style.transform = 'scale(1.02)';
            parentGroup.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
            parentGroup.style.borderColor = '#667eea';

            // 清空并逐步填充数据
            textarea.value = '';

            setTimeout(() => {
                textarea.value = data;

                // 显示数据行数提示
                const dataLines = data.split('\n').length;
                const header = parentGroup.querySelector('h4');
                const originalText = header.textContent;
                header.innerHTML = `${originalText} <span style="color: #4caf50; font-size: 0.8em;">(已填充 ${dataLines} 行)</span>`;

                // 恢复原始样式
                setTimeout(() => {
                    parentGroup.style.transform = 'scale(1)';
                    parentGroup.style.boxShadow = '';
                    parentGroup.style.borderColor = '#e1e8ed';

                    // 3秒后移除行数提示
                    setTimeout(() => {
                        header.textContent = originalText;
                    }, 3000);
                }, 500);
            }, 200);
        }

        // 生成数据预览表格（带时间戳）
        function generateDataPreviewWithTimestamp(product1Data, product2Data, product3Data, observedData) {
            const maxRows = Math.min(5, product1Data.length);
            const hasObserved = observedData.length === product1Data.length;

            let previewHtml = `
                <div style="overflow-x: auto; margin-top: 8px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 10px; background: white; border-radius: 4px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">行号</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">时间戳</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品1</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品2</th>
                                <th style="padding: 4px; border: 1px solid #ddd; text-align: center;">产品3</th>
                                ${hasObserved ? '<th style="padding: 4px; border: 1px solid #ddd; text-align: center;">实测雨量</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
            `;

            for (let i = 0; i < maxRows; i++) {
                // 解析时间戳和数值
                const product1Parts = product1Data[i].split(',');
                const product2Parts = product2Data[i].split(',');
                const product3Parts = product3Data[i].split(',');

                const timestamp = product1Parts[0];
                const val1 = parseFloat(product1Parts[1]).toFixed(3);
                const val2 = parseFloat(product2Parts[1]).toFixed(3);
                const val3 = parseFloat(product3Parts[1]).toFixed(3);

                let observedVal = '';
                if (hasObserved && observedData[i]) {
                    const observedParts = observedData[i].split(',');
                    observedVal = parseFloat(observedParts[1]).toFixed(3);
                }

                previewHtml += `
                    <tr>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center; font-weight: 600;">${i + 1}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center; font-size: 9px;">${timestamp}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val1}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val2}</td>
                        <td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${val3}</td>
                        ${hasObserved ? `<td style="padding: 4px; border: 1px solid #ddd; text-align: center;">${observedVal}</td>` : ''}
                    </tr>
                `;
            }

            previewHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            if (product1Data.length > 5) {
                previewHtml += `<div style="text-align: center; margin-top: 5px; font-size: 10px; color: #666;">... 还有 ${product1Data.length - 5} 行数据</div>`;
            }

            return previewHtml;
        }

        // 生成数据预览表格（兼容旧格式）
        function generateDataPreview(product1Data, product2Data, product3Data, observedData) {
            const maxRows = Math.min(5, product1Data.length);
            const hasObserved = observedData.length === product1Data.length;

            let previewHtml = `
                <div style="overflow-x: auto; margin-top: 8px;">
                    <table style="width: 100%; border-collapse: collapse; font-size: 11px; background: white; border-radius: 4px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">行号</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品1</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品2</th>
                                <th style="padding: 6px; border: 1px solid #ddd; text-align: center;">产品3</th>
                                ${hasObserved ? '<th style="padding: 6px; border: 1px solid #ddd; text-align: center;">实测雨量</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
            `;

            for (let i = 0; i < maxRows; i++) {
                previewHtml += `
                    <tr>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center; font-weight: 600;">${i + 1}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product1Data[i].toFixed(3)}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product2Data[i].toFixed(3)}</td>
                        <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${product3Data[i].toFixed(3)}</td>
                        ${hasObserved ? `<td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${observedData[i].toFixed(3)}</td>` : ''}
                    </tr>
                `;
            }

            previewHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            if (product1Data.length > 5) {
                previewHtml += `<div style="text-align: center; margin-top: 5px; font-size: 10px; color: #666;">... 还有 ${product1Data.length - 5} 行数据</div>`;
            }

            return previewHtml;
        }

        // 自动生成时间序列表格
        function generateTimeSeriesTables() {
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;
            const frequency = parseInt(document.getElementById('frequency').value);

            if (!startTime || !endTime) {
                alert('请先设置起始时间和结束时间');
                return;
            }

            const start = new Date(startTime);
            const end = new Date(endTime);

            if (start >= end) {
                alert('结束时间必须晚于起始时间');
                return;
            }

            // 生成时间戳数组
            const timestamps = [];
            let current = new Date(start);

            while (current <= end) {
                timestamps.push(current.toISOString().slice(0, 19).replace('T', ' '));
                current = new Date(current.getTime() + frequency * 3600000); // 增加频率小时数
            }

            // 为每个产品表格生成时间序列
            const products = ['product1', 'product2', 'product3', 'observed'];

            products.forEach(productName => {
                // 获取现有数据
                const existingData = getTableData(productName);
                const existingValues = existingData.map(item => item.value);

                // 清空表格
                clearTable(productName);

                // 为每个时间戳添加行
                timestamps.forEach((timestamp, index) => {
                    const value = existingValues[index] || 0.0;
                    addTableRow(productName, timestamp, value);
                });
            });

            alert(`已为 ${timestamps.length} 个时间点生成时间序列表格`);
        }

        // 从数据中提取数值
        function extractValuesFromData(dataString) {
            const lines = dataString.split('\n');
            const values = [];

            lines.forEach(line => {
                const trimmedLine = line.trim();
                if (!trimmedLine) return;

                if (trimmedLine.includes(',')) {
                    // 已有时间戳格式，提取数值部分
                    const parts = trimmedLine.split(',');
                    if (parts.length >= 2) {
                        const value = parseFloat(parts[1].trim());
                        if (!isNaN(value)) {
                            values.push(value);
                        }
                    }
                } else {
                    // 纯数值格式
                    const value = parseFloat(trimmedLine);
                    if (!isNaN(value)) {
                        values.push(value);
                    }
                }
            });

            return values;
        }

        // 生成带时间戳的数据
        function generateTimestampedData(timestamps, values) {
            const result = [];
            const maxLength = Math.min(timestamps.length, values.length);

            for (let i = 0; i < maxLength; i++) {
                result.push(`${timestamps[i]},${values[i]}`);
            }

            // 如果时间戳多于数值，用0填充
            for (let i = values.length; i < timestamps.length; i++) {
                result.push(`${timestamps[i]},0.0`);
            }

            return result.join('\n');
        }

        // 清空所有数据
        function clearAllData() {
            if (confirm('确定要清空所有输入的数据吗？')) {
                // 清空所有表格
                clearTable('product1');
                clearTable('product2');
                clearTable('product3');
                clearTable('observed');

                // 清空文件上传状态
                document.getElementById('fileUploadStatus').style.display = 'none';
                document.getElementById('excelFileInput').value = '';

                // 重置所有标题
                const headers = document.querySelectorAll('.data-input-group h4');
                headers.forEach(header => {
                    const originalTexts = ['产品1预报数据', '产品2预报数据', '产品3预报数据', '实测雨量 (可选)'];
                    const index = Array.from(headers).indexOf(header);
                    if (index < originalTexts.length) {
                        header.textContent = originalTexts[index];
                    }
                });

                // 显示清空成功提示
                const statusDiv = document.getElementById('fileUploadStatus');
                statusDiv.style.display = 'block';
                statusDiv.className = 'file-upload-status success';
                statusDiv.innerHTML = '✅ 所有数据已清空';

                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 2000);
            }
        }

        // 初始化表格
        function initializeTables() {
            const products = ['product1', 'product2', 'product3', 'observed'];
            products.forEach(productName => {
                // 为每个表格添加几行示例数据
                addTableRow(productName, '2024-01-01 09:00', '0.0');
                addTableRow(productName, '2024-01-01 10:00', '0.0');
                addTableRow(productName, '2024-01-01 11:00', '0.0');
            });
        }

        // 添加拖拽上传功能
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化表格
            initializeTables();

            const uploadArea = document.querySelector('.file-upload-area');

            if (uploadArea) {
                uploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        const file = files[0];
                        if (file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                            document.getElementById('excelFileInput').files = files;
                            handleFileUpload({ target: { files: files } });
                        } else {
                            showUploadError('请上传Excel文件 (.xlsx 或 .xls 格式)');
                        }
                    }
                });
            }
        });

        function calculateStats(data, name) {
            if (data.length === 0) return null;
            
            const mean = data.reduce((a, b) => a + b, 0) / data.length;
            const variance = data.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / data.length;
            const std = Math.sqrt(variance);
            const min = Math.min(...data);
            const max = Math.max(...data);
            const nonZeroCount = data.filter(val => val > 0).length;
            
            return {
                name,
                mean: mean.toFixed(3),
                std: std.toFixed(3),
                min: min.toFixed(3),
                max: max.toFixed(3),
                nonZeroCount,
                total: data.length
            };
        }

        // 折叠/展开功能
        function toggleCollapsible(header) {
            const section = header.parentElement;
            const content = section.querySelector('.collapsible-content');
            const toggle = section.querySelector('.collapsible-toggle');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                section.classList.remove('expanded');
                content.style.display = 'none';
            } else {
                content.classList.add('active');
                section.classList.add('expanded');
                content.style.display = 'block';
            }
        }

        // 分页功能
        function createPagination(totalItems, container) {
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            let paginationHtml = `
                <div class="pagination-container">
                    <button class="pagination-btn" onclick="changePage(1)" ${currentPage === 1 ? 'disabled' : ''}>
                        ⏮️ 首页
                    </button>
                    <button class="pagination-btn" onclick="changePage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                        ⬅️ 上一页
                    </button>
                    <span class="pagination-info">
                        第 ${currentPage} 页，共 ${totalPages} 页 (${totalItems} 条记录)
                    </span>
                    <button class="pagination-btn" onclick="changePage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                        下一页 ➡️
                    </button>
                    <button class="pagination-btn" onclick="changePage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>
                        末页 ⏭️
                    </button>
                </div>
            `;
            
            const paginationDiv = document.createElement('div');
            paginationDiv.innerHTML = paginationHtml;
            container.appendChild(paginationDiv);
        }

        function changePage(page) {
            currentPage = page;
            displayPaginatedTable();
        }

        function displayPaginatedTable() {
            const container = document.getElementById('paginatedTableContainer');
            container.innerHTML = '';
            
            if (allDataPoints.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, allDataPoints.length);
            const pageData = allDataPoints.slice(startIndex, endIndex);
            
            let tableHtml = `
                <div class="table-container">
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>时间</th>
                                <th>产品1 (mm)</th>
                                <th>产品2 (mm)</th>
                                <th>产品3 (mm)</th>
                                <th>融合结果 (mm)</th>
                                ${allDataPoints[0].observed !== undefined ? '<th>实测雨量 (mm)</th>' : ''}
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            pageData.forEach((item, index) => {
                const globalIndex = startIndex + index + 1;
                tableHtml += `
                    <tr>
                        <td>${globalIndex}</td>
                        <td>${item.time}</td>
                        <td>${item.product1.toFixed(3)}</td>
                        <td>${item.product2.toFixed(3)}</td>
                        <td>${item.product3.toFixed(3)}</td>
                        <td style="font-weight: 600; color: #2e7d32;">${item.fusedResult.toFixed(3)}</td>
                        ${item.observed !== undefined ? `<td>${item.observed.toFixed(3)}</td>` : ''}
                    </tr>
                `;
            });
            
            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;
            
            container.innerHTML = tableHtml;
            createPagination(allDataPoints.length, container);
        }

        function displayRawDataPreview() {
            const container = document.getElementById('rawDataPreview');
            
            if (allDataPoints.length === 0) {
                container.innerHTML = '<p>暂无数据</p>';
                return;
            }
            
            const previewCount = 5;
            const hasObserved = allDataPoints[0].observed !== undefined;
            
            let html = `
                <div class="data-preview">
                    <div class="data-preview-header">
                        <h5>📊 数据概览</h5>
                        <div class="data-preview-stats">
                            总计 ${allDataPoints.length} 条记录，显示前 ${Math.min(previewCount, allDataPoints.length)} 条
                        </div>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="stats-table" style="min-width: 600px;">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>产品1</th>
                                    <th>产品2</th>
                                    <th>产品3</th>
                                    <th>融合结果</th>
                                    ${hasObserved ? '<th>实测雨量</th>' : ''}
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            for (let i = 0; i < Math.min(previewCount, allDataPoints.length); i++) {
                const item = allDataPoints[i];
                html += `
                    <tr>
                        <td>${item.time}</td>
                        <td>${item.product1.toFixed(3)}</td>
                        <td>${item.product2.toFixed(3)}</td>
                        <td>${item.product3.toFixed(3)}</td>
                        <td style="font-weight: 600; color: #2e7d32;">${item.fusedResult.toFixed(3)}</td>
                        ${hasObserved ? `<td>${item.observed.toFixed(3)}</td>` : ''}
                    </tr>
                `;
            }
            
            html += `
                            </tbody>
                        </table>
                    </div>
                    ${allDataPoints.length > previewCount ? 
                        `<div style="text-align: center; margin-top: 15px;">
                            <button class="view-full-btn" onclick="switchTab('detailed'); document.querySelector('.collapsible-section:nth-child(2) .collapsible-header').click();">
                                查看完整数据表格
                            </button>
                        </div>` : ''
                    }
                </div>
            `;
            
            container.innerHTML = html;
        }        function showMainView() {
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('welcomeMessage').style.display = 'block';
            document.getElementById('currentPage').textContent = '主页';
        }

        // 加载指示器函数
        function showLoading(text = '正在处理数据...') {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingOverlay').classList.add('active');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.remove('active');
        }function performStatisticalAnalysis() {
            showLoading('正在进行统计分析...');
            
            setTimeout(() => {
                const product1 = parseData(document.getElementById('product1Data').value);
                const product2 = parseData(document.getElementById('product2Data').value);
                const product3 = parseData(document.getElementById('product3Data').value);
                const observed = parseData(document.getElementById('observedData').value);

                if (product1.length === 0 || product2.length === 0 || product3.length === 0) {
                    hideLoading();
                    showError('请确保至少输入三个预报产品的数据！');
                    return;
                }

                if (product1.length !== product2.length || product2.length !== product3.length) {
                    hideLoading();
                    showError('三个预报产品的数据长度必须一致！');
                    return;
                }

                if (observed.length > 0 && observed.length !== product1.length) {
                    hideLoading();
                    showError('实测雨量数据长度必须与预报数据一致！');
                    return;
                }

                const stats = [];
                stats.push(calculateStats(product1, '产品1'));
                stats.push(calculateStats(product2, '产品2'));
                stats.push(calculateStats(product3, '产品3'));
                
                if (observed.length > 0) {
                    stats.push(calculateStats(observed, '实测雨量'));
                }

                // 准备所有数据点
                prepareDataPoints(product1, product2, product3, observed);
                displayStatsTable(stats);
                createStatsCharts(product1, product2, product3, observed);
                displayRawDataPreview();
                document.getElementById('welcomeMessage').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('currentPage').textContent = '统计分析';
                switchTab('stats');
                
                hideLoading();
                
                // 滚动到结果区域
                setTimeout(() => {
                    scrollToSection('resultsSection');
                }, 100);
            }, 500); // 模拟处理时间
        }

        function prepareDataPoints(product1, product2, product3, observed) {
            const startTime = new Date(document.getElementById('startTime').value);
            const frequency = parseInt(document.getElementById('frequency').value);
            
            // 计算融合数据
            const weights = [0.35, 0.35, 0.3];
            const fusedData = product1.map((val, index) => {
                return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
            });

            allDataPoints = [];
            for (let i = 0; i < product1.length; i++) {
                const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                const timeStr = currentTime.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
                
                const dataPoint = {
                    time: timeStr,
                    product1: product1[i],
                    product2: product2[i],
                    product3: product3[i],
                    fusedResult: fusedData[i]
                };
                
                if (observed.length > 0) {
                    dataPoint.observed = observed[i];
                }
                
                allDataPoints.push(dataPoint);
            }
            
            // 重置分页
            currentPage = 1;
        }        function displayStatsTable(stats) {
            const container = document.getElementById('statsTableContainer');
            
            let html = `
                <div class="table-container">
                    <table class="stats-table">
                        <thead>
                            <tr>
                                <th>数据源</th>
                                <th>均值 (mm)</th>
                                <th>标准差 (mm)</th>
                                <th>最小值 (mm)</th>
                                <th>最大值 (mm)</th>
                                <th>非零数量</th>
                                <th>总数量</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            stats.forEach(stat => {
                html += `
                    <tr>
                        <td style="font-weight: 600;">${stat.name}</td>
                        <td>${stat.mean}</td>
                        <td>${stat.std}</td>
                        <td>${stat.min}</td>
                        <td>${stat.max}</td>
                        <td>${stat.nonZeroCount}</td>
                        <td>${stat.total}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            container.innerHTML = html;
        }

        // 创建统计分析图表
        let statsCharts = {};

        function createStatsCharts(product1, product2, product3, observed) {
            // 销毁已存在的图表
            Object.values(statsCharts).forEach(chart => {
                if (chart) chart.destroy();
            });
            statsCharts = {};

            // 生成时间标签
            const startTime = new Date(document.getElementById('startTime').value);
            const frequency = parseInt(document.getElementById('frequency').value);
            const labels = [];

            for (let i = 0; i < product1.length; i++) {
                const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                labels.push(currentTime.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }));
            }

            // 创建产品1图表
            createSingleChart('product1Chart', '产品1预报数据', product1, labels, '#ff8c00');

            // 创建产品2图表
            createSingleChart('product2Chart', '产品2预报数据', product2, labels, '#9b59b6');

            // 创建产品3图表
            createSingleChart('product3Chart', '产品3预报数据', product3, labels, '#3498db');

            // 创建实测雨量图表（如果有数据）
            if (observed.length > 0) {
                createSingleChart('observedChart', '实测雨量数据', observed, labels, '#27ae60');
            } else {
                // 如果没有实测数据，显示提示
                const ctx = document.getElementById('observedChart');
                if (ctx) {
                    const container = ctx.parentElement;
                    container.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666; font-size: 14px;">暂无实测雨量数据</div>';
                }
            }
        }

        function createSingleChart(canvasId, title, data, labels, color) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            statsCharts[canvasId] = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: title,
                        data: data,
                        borderColor: color,
                        backgroundColor: color.replace('rgb', 'rgba').replace(')', ', 0.1)'),
                        borderWidth: 1,
                        pointRadius: 0,
                        pointHoverRadius: 0,
                        pointStyle: false,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '降雨量 (mm)',
                                font: {
                                    size: 11
                                }
                            },
                            ticks: {
                                font: {
                                    size: 10
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 11
                                }
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                font: {
                                    size: 9
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }        function performDataFusion() {
            showLoading('正在进行数据融合...');
            
            setTimeout(() => {
                const product1 = parseData(document.getElementById('product1Data').value);
                const product2 = parseData(document.getElementById('product2Data').value);
                const product3 = parseData(document.getElementById('product3Data').value);
                const observed = parseData(document.getElementById('observedData').value);

                if (product1.length === 0 || product2.length === 0 || product3.length === 0) {
                    hideLoading();
                    showError('请确保至少输入三个预报产品的数据！');
                    return;
                }

                if (product1.length !== product2.length || product2.length !== product3.length) {
                    hideLoading();
                    showError('三个预报产品的数据长度必须一致！');
                    return;
                }

                if (observed.length > 0 && observed.length !== product1.length) {
                    hideLoading();
                    showError('实测雨量数据长度必须与预报数据一致！');
                    return;
                }

                // 简单加权平均融合 (可以根据需要调整权重)
                const weights = [0.35, 0.35, 0.3]; // 三个产品的权重
                const fusedData = product1.map((val, index) => {
                    return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
                });

                // 准备所有数据点
                prepareDataPoints(product1, product2, product3, observed);                displayFusionResults(product1, product2, product3, fusedData, observed);
                createFusionChart(product1, product2, product3, fusedData, observed);
                displayRawDataPreview();
                
                if (observed.length > 0) {
                    calculateAndDisplayMetrics(product1, product2, product3, fusedData, observed);
                }

                document.getElementById('welcomeMessage').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';
                document.getElementById('currentPage').textContent = '融合结果';
                switchTab('fusion');
                
                hideLoading();
                
                // 滚动到结果区域
                setTimeout(() => {
                    scrollToSection('resultsSection');
                }, 100);
            }, 800); // 模拟处理时间
        }

        function displayFusionResults(product1, product2, product3, fusedData, observed) {
            const container = document.getElementById('fusionResults');
            
            let html = `
                <div class="fusion-result">
                    <h4>📁 融合结果导出</h4>
                    <div style="display: flex; gap: 15px; margin-top: 15px; flex-wrap: wrap;">
                        <button type="button" style="background: #28a745; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('csv')" onmouseover="this.style.background='#218838'" onmouseout="this.style.background='#28a745'">
                            📊 导出CSV格式
                        </button>
                        <button type="button" style="background: #17a2b8; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('json')" onmouseover="this.style.background='#138496'" onmouseout="this.style.background='#17a2b8'">
                            📋 导出JSON格式
                        </button>
                        <button type="button" style="background: #6f42c1; color: white; padding: 10px 20px; border-radius: 8px; border: none; cursor: pointer; font-size: 14px; transition: all 0.3s ease;" onclick="exportFusionData('txt')" onmouseover="this.style.background='#5a32a3'" onmouseout="this.style.background='#6f42c1'">
                            📄 导出TXT格式
                        </button>
                    </div>
                    <div style="margin-top: 15px; font-size: 14px; color: #666;">
                        融合结果包含 ${fusedData.length} 个时次的预报数据
                    </div>
                </div>
            `;

            if (observed.length === 0) {
                html += `
                    <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 10px; border-left: 5px solid #ffc107;">
                        <strong>💡 提示:</strong> 未提供实测雨量数据，仅显示融合结果。如需评估融合效果，请输入实测雨量数据。
                    </div>
                `;
            }

            container.innerHTML = html;
        }

        function createFusionChart(product1, product2, product3, fusedData, observed) {
            const ctx = document.getElementById('fusionChart').getContext('2d');
            
            if (currentChart) {
                currentChart.destroy();
            }

            // 生成时间标签
            const startTime = new Date(document.getElementById('startTime').value);
            const frequency = parseInt(document.getElementById('frequency').value);
            const labels = [];
            
            for (let i = 0; i < product1.length; i++) {
                const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                labels.push(currentTime.toLocaleString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                }));
            }
            
            const datasets = [
                {
                    label: '产品1',
                    data: product1,
                    borderColor: '#ff8c00',
                    backgroundColor: 'rgba(255, 140, 0, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '产品2',
                    data: product2,
                    borderColor: '#9b59b6',
                    backgroundColor: 'rgba(155, 89, 182, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '产品3',
                    data: product3,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 1,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    borderDash: [5, 5]
                },
                {
                    label: '融合结果',
                    data: fusedData,
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.2)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    pointBackgroundColor: '#e74c3c'
                }
            ];

            if (observed.length > 0) {
                datasets.push({
                    label: '实测雨量',
                    data: observed,
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.2)',
                    borderWidth: 2,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                    pointStyle: false,
                    pointBackgroundColor: '#27ae60'
                });
            }

            currentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '多源降雨产品融合对比图',
                            font: {
                                size: 16,
                                weight: 'bold'
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '降雨量 (mm)',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: '时间',
                                font: {
                                    size: 14,
                                    weight: 'bold'
                                }
                            },
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        function calculateMetrics(forecast, observed) {
            const n = forecast.length;
            
            // 相关系数
            const meanForecast = forecast.reduce((a, b) => a + b, 0) / n;
            const meanObserved = observed.reduce((a, b) => a + b, 0) / n;
            
            let numerator = 0;
            let denomForecast = 0;
            let denomObserved = 0;
            
            for (let i = 0; i < n; i++) {
                numerator += (forecast[i] - meanForecast) * (observed[i] - meanObserved);
                denomForecast += Math.pow(forecast[i] - meanForecast, 2);
                denomObserved += Math.pow(observed[i] - meanObserved, 2);
            }
            
            const correlation = numerator / Math.sqrt(denomForecast * denomObserved);
            
            // 均方根误差
            const rmse = Math.sqrt(forecast.reduce((sum, val, i) => sum + Math.pow(val - observed[i], 2), 0) / n);
            
            // 纳什效率系数
            const ssRes = forecast.reduce((sum, val, i) => sum + Math.pow(observed[i] - val, 2), 0);
            const ssTot = observed.reduce((sum, val) => sum + Math.pow(val - meanObserved, 2), 0);
            const nash = 1 - (ssRes / ssTot);
            
            return {
                correlation: correlation.toFixed(4),
                rmse: rmse.toFixed(4),
                nash: nash.toFixed(4)
            };
        }        function calculateAndDisplayMetrics(product1, product2, product3, fusedData, observed) {
            const metrics1 = calculateMetrics(product1, observed);
            const metrics2 = calculateMetrics(product2, observed);
            const metrics3 = calculateMetrics(product3, observed);
            const metricsFused = calculateMetrics(fusedData, observed);

            const container = document.getElementById('metricsTableContainer');
            
            const html = `
                <div class="table-container">
                    <table class="metrics-table">
                        <thead>
                            <tr>
                                <th>产品</th>
                                <th>纳什效率系数 (NSE)</th>
                                <th>相关系数 (R)</th>
                                <th>均方根误差 (RMSE)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="font-weight: 600;">产品1</td>
                                <td>${metrics1.nash}</td>
                                <td>${metrics1.correlation}</td>
                                <td>${metrics1.rmse}</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品2</td>
                                <td>${metrics2.nash}</td>
                                <td>${metrics2.correlation}</td>
                                <td>${metrics2.rmse}</td>
                            </tr>
                            <tr>
                                <td style="font-weight: 600;">产品3</td>
                                <td>${metrics3.nash}</td>
                                <td>${metrics3.correlation}</td>
                                <td>${metrics3.rmse}</td>
                            </tr>
                            <tr style="background: #e8f5e8; font-weight: 600;">
                                <td style="color: #2e7d32;">融合产品</td>
                                <td style="color: #2e7d32;">${metricsFused.nash}</td>
                                <td style="color: #2e7d32;">${metricsFused.correlation}</td>
                                <td style="color: #2e7d32;">${metricsFused.rmse}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-radius: 10px; border-left: 5px solid #2196f3;">
                    <strong>📊 指标说明:</strong>
                    <ul style="margin-top: 10px; margin-left: 20px;">
                        <li><strong>NSE (纳什效率系数):</strong> 值越接近1表示预报效果越好</li>
                        <li><strong>R (相关系数):</strong> 值越接近1表示相关性越强</li>
                        <li><strong>RMSE (均方根误差):</strong> 值越小表示预报误差越小</li>
                    </ul>
                </div>
            `;

            container.innerHTML = html;
        }        function switchTab(tabName) {
            // 切换标签页样式
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            if (tabName === 'stats') {
                document.querySelector('.tab:nth-child(1)').classList.add('active');
                document.getElementById('statsContent').classList.add('active');
                document.getElementById('currentPage').textContent = '统计分析';
            } else if (tabName === 'fusion') {
                document.querySelector('.tab:nth-child(2)').classList.add('active');
                document.getElementById('fusionContent').classList.add('active');
                document.getElementById('currentPage').textContent = '融合结果';
            } else if (tabName === 'detailed') {
                document.querySelector('.tab:nth-child(3)').classList.add('active');
                document.getElementById('detailedContent').classList.add('active');
                document.getElementById('currentPage').textContent = '详细数据';
                // 显示分页表格
                displayPaginatedTable();
            }
        }

        function showError(message) {
            const resultsSection = document.getElementById('resultsSection');
            resultsSection.innerHTML = `
                <div class="error-message">
                    <strong>❌ 错误:</strong> ${message}
                </div>
            `;
            resultsSection.style.display = 'block';
        }        // 示例数据填充
        function fillSampleData() {
            // 生成更多示例数据以展示分页功能
            const generateRandomData = (baseValue, variation, count) => {
                const data = [];
                for (let i = 0; i < count; i++) {
                    const value = baseValue + (Math.random() - 0.5) * variation;
                    data.push(Math.max(0, value).toFixed(1));
                }
                return data.join('\n');
            };

            document.getElementById('product1Data').value = generateRandomData(1.5, 3.0, 25);
            document.getElementById('product2Data').value = generateRandomData(1.3, 2.8, 25);
            document.getElementById('product3Data').value = generateRandomData(1.7, 3.2, 25);
            document.getElementById('observedData').value = generateRandomData(1.6, 2.9, 25);
        }        // 页面加载完成后填充示例数据
        window.addEventListener('load', function() {
            fillSampleData();
            initScrollEffects();
        });

        // 滚动效果初始化
        function initScrollEffects() {
            const backToTop = document.getElementById('backToTop');
            const indicators = document.querySelectorAll('.indicator-dot');
            
            window.addEventListener('scroll', function() {
                // 回到顶部按钮显示/隐藏
                if (window.pageYOffset > 300) {
                    backToTop.classList.add('visible');
                } else {
                    backToTop.classList.remove('visible');
                }
                
                // 更新页面指示器
                updatePageIndicator();
            });
        }

        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId) || document.querySelector('.' + sectionId);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        function updatePageIndicator() {
            const indicators = document.querySelectorAll('.indicator-dot');
            const sections = [
                document.querySelector('.header'),
                document.querySelector('.input-section'),
                document.getElementById('resultsSection')
            ];
            
            let currentSection = 0;
            const scrollPosition = window.pageYOffset + window.innerHeight / 2;
            
            sections.forEach((section, index) => {
                if (section && section.offsetTop <= scrollPosition) {
                    currentSection = index;
                }
            });
            
            indicators.forEach((indicator, index) => {
                indicator.classList.toggle('active', index === currentSection);
            });
        }

        // 导出融合数据功能
        function exportFusionData(format) {
            const product1 = parseData(document.getElementById('product1Data').value);
            const product2 = parseData(document.getElementById('product2Data').value);
            const product3 = parseData(document.getElementById('product3Data').value);
            const observed = parseData(document.getElementById('observedData').value);

            if (product1.length === 0 || product2.length === 0 || product3.length === 0) {
                alert('请先进行数据融合！');
                return;
            }

            // 重新计算融合数据
            const weights = [0.35, 0.35, 0.3];
            const fusedData = product1.map((val, index) => {
                return weights[0] * product1[index] + weights[1] * product2[index] + weights[2] * product3[index];
            });

            // 生成时间序列
            const startTime = new Date(document.getElementById('startTime').value);
            const frequency = parseInt(document.getElementById('frequency').value);
            const timeLabels = [];
            
            for (let i = 0; i < fusedData.length; i++) {
                const currentTime = new Date(startTime.getTime() + i * frequency * 60 * 60 * 1000);
                timeLabels.push(currentTime.toISOString().slice(0, 16).replace('T', ' '));
            }

            let content = '';
            let filename = '';
            let mimeType = '';

            if (format === 'csv') {
                content = '时间,产品1,产品2,产品3,融合结果';
                if (observed.length > 0) content += ',实测雨量';
                content += '\n';
                
                for (let i = 0; i < fusedData.length; i++) {
                    content += `${timeLabels[i]},${product1[i].toFixed(3)},${product2[i].toFixed(3)},${product3[i].toFixed(3)},${fusedData[i].toFixed(3)}`;
                    if (observed.length > 0) content += `,${observed[i].toFixed(3)}`;
                    content += '\n';
                }
                filename = '降雨融合结果.csv';
                mimeType = 'text/csv;charset=utf-8;';
            } else if (format === 'json') {
                const jsonData = {
                    metadata: {
                        startTime: document.getElementById('startTime').value,
                        endTime: document.getElementById('endTime').value,
                        frequency: frequency + '小时',
                        exportTime: new Date().toISOString(),
                        dataPoints: fusedData.length
                    },
                    data: timeLabels.map((time, i) => {
                        const point = {
                            time: time,
                            product1: parseFloat(product1[i].toFixed(3)),
                            product2: parseFloat(product2[i].toFixed(3)),
                            product3: parseFloat(product3[i].toFixed(3)),
                            fusedResult: parseFloat(fusedData[i].toFixed(3))
                        };
                        if (observed.length > 0) {
                            point.observed = parseFloat(observed[i].toFixed(3));
                        }
                        return point;
                    })
                };
                content = JSON.stringify(jsonData, null, 2);
                filename = '降雨融合结果.json';
                mimeType = 'application/json;charset=utf-8;';
            } else if (format === 'txt') {
                content = '多源降雨产品融合结果\n';
                content += '=' .repeat(50) + '\n';
                content += `起始时间: ${document.getElementById('startTime').value}\n`;
                content += `结束时间: ${document.getElementById('endTime').value}\n`;
                content += `预报频率: ${frequency}小时\n`;
                content += `数据点数: ${fusedData.length}\n`;
                content += `导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;
                
                for (let i = 0; i < fusedData.length; i++) {
                    content += `时间: ${timeLabels[i]}\n`;
                    content += `  产品1: ${product1[i].toFixed(3)} mm\n`;
                    content += `  产品2: ${product2[i].toFixed(3)} mm\n`;
                    content += `  产品3: ${product3[i].toFixed(3)} mm\n`;
                    content += `  融合结果: ${fusedData[i].toFixed(3)} mm\n`;
                    if (observed.length > 0) {
                        content += `  实测雨量: ${observed[i].toFixed(3)} mm\n`;
                    }
                    content += '\n';
                }
                filename = '降雨融合结果.txt';
                mimeType = 'text/plain;charset=utf-8;';
            }

            // 创建下载链接
            const blob = new Blob(['\ufeff' + content], { type: mimeType });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();            document.body.removeChild(link);
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            // Ctrl + 数字键切换标签页
            if (e.ctrlKey) {
                if (e.key === '1') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('stats');
                    }
                } else if (e.key === '2') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('fusion');
                    }
                } else if (e.key === '3') {
                    e.preventDefault();
                    if (document.getElementById('resultsSection').style.display !== 'none') {
                        switchTab('detailed');
                    }
                }
            }
            
            // 分页导航
            if (document.getElementById('detailedContent').classList.contains('active')) {
                if (e.key === 'ArrowLeft' && currentPage > 1) {
                    e.preventDefault();
                    changePage(currentPage - 1);
                } else if (e.key === 'ArrowRight') {
                    const totalPages = Math.ceil(allDataPoints.length / itemsPerPage);
                    if (currentPage < totalPages) {
                        e.preventDefault();
                        changePage(currentPage + 1);
                    }
                }
            }
            
            // ESC键返回主页
            if (e.key === 'Escape') {
                showMainView();
            }
        });
    </script>
</body>
</html>